Metadata-Version: 2.4
Name: shapely
Version: 2.1.1
Summary: Manipulation and analysis of geometric objects
Author: <PERSON>
Maintainer: Shapely contributors
License: BSD 3-Clause
Project-URL: Documentation, https://shapely.readthedocs.io/
Project-URL: Repository, https://github.com/shapely/shapely
Keywords: geometry,topology,gis
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: Unix
Classifier: Operating System :: MacOS
Classifier: Operating System :: Microsoft :: Windows
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Topic :: Scientific/Engineering :: GIS
Requires-Python: >=3.10
Description-Content-Type: text/x-rst
License-File: LICENSE.txt
License-File: LICENSE_GEOS
License-File: LICENSE_win32
Requires-Dist: numpy>=1.21
Provides-Extra: test
Requires-Dist: pytest; extra == "test"
Requires-Dist: pytest-cov; extra == "test"
Requires-Dist: scipy-doctest; extra == "test"
Provides-Extra: docs
Requires-Dist: numpydoc==1.1.*; extra == "docs"
Requires-Dist: matplotlib; extra == "docs"
Requires-Dist: sphinx; extra == "docs"
Requires-Dist: sphinx-book-theme; extra == "docs"
Requires-Dist: sphinx-remove-toctrees; extra == "docs"
Dynamic: license-file

=======
Shapely
=======

.. Documentation at RTD — https://readthedocs.org

.. image:: https://readthedocs.org/projects/shapely/badge/?version=stable
   :alt: Documentation Status
   :target: https://shapely.readthedocs.io/en/stable/

.. Github Actions status — https://github.com/shapely/shapely/actions

.. |github-actions| image:: https://github.com/shapely/shapely/workflows/Tests/badge.svg?branch=main
   :alt: Github Actions status
   :target: https://github.com/shapely/shapely/actions?query=branch%3Amain

.. PyPI

.. image:: https://img.shields.io/pypi/v/shapely.svg
   :alt: PyPI
   :target: https://pypi.org/project/shapely/

.. Anaconda

.. image:: https://img.shields.io/conda/vn/conda-forge/shapely
   :alt: Anaconda
   :target: https://anaconda.org/conda-forge/shapely

.. Coverage

.. |coveralls| image:: https://coveralls.io/repos/github/shapely/shapely/badge.svg?branch=main
   :target: https://coveralls.io/github/shapely/shapely?branch=main

.. Zenodo

.. .. image:: https://zenodo.org/badge/191151963.svg
..   :alt: Zenodo
..   :target: https://zenodo.org/badge/latestdoi/191151963

Manipulation and analysis of geometric objects in the Cartesian plane.

.. image:: https://c2.staticflickr.com/6/5560/31301790086_b3472ea4e9_c.jpg
   :width: 800
   :height: 378

Shapely is a BSD-licensed Python package for manipulation and analysis of
planar geometric objects. It is using the widely deployed open-source
geometry library `GEOS <https://libgeos.org/>`__ (the engine of `PostGIS
<https://postgis.net/>`__, and a port of `JTS <https://locationtech.github.io/jts/>`__).
Shapely wraps GEOS geometries and operations to provide both a feature rich
`Geometry` interface for singular (scalar) geometries and higher-performance
NumPy ufuncs for operations using arrays of geometries.
Shapely is not primarily focused on data serialization formats or coordinate
systems, but can be readily integrated with packages that are.

What is a ufunc?
----------------

A universal function (or ufunc for short) is a function that operates on
*n*-dimensional arrays on an element-by-element fashion and supports array
broadcasting. The underlying ``for`` loops are implemented in C to reduce the
overhead of the Python interpreter.

Multithreading
--------------

Shapely functions generally support multithreading by releasing the Global
Interpreter Lock (GIL) during execution. Normally in Python, the GIL prevents
multiple threads from computing at the same time. Shapely functions
internally release this constraint so that the heavy lifting done by GEOS can
be done in parallel, from a single Python process.

Usage
=====

Here is the canonical example of building an approximately circular patch by
buffering a point, using the scalar Geometry interface:

.. code-block:: pycon

    >>> from shapely import Point
    >>> patch = Point(0.0, 0.0).buffer(10.0)
    >>> patch
    <POLYGON ((10 0, 9.952 -0.98, 9.808 -1.951, 9.569 -2.903, 9.239 -3.827, 8.81...>
    >>> patch.area
    313.6548490545941

Using the vectorized ufunc interface (instead of using a manual for loop),
compare an array of points with a polygon:

.. code:: python

    >>> import shapely
    >>> import numpy as np
    >>> geoms = np.array([Point(0, 0), Point(1, 1), Point(2, 2)])
    >>> polygon = shapely.box(0, 0, 2, 2)

    >>> shapely.contains(polygon, geoms)
    array([False,  True, False])

See the documentation for more examples and guidance: https://shapely.readthedocs.io

Requirements
============

Shapely 2.1 requires

* Python >=3.10
* GEOS >=3.9
* NumPy >=1.21

Installing Shapely
==================

We recommend installing Shapely using one of the available built
distributions, for example using ``pip`` or ``conda``:

.. code-block:: console

    $ pip install shapely
    # or using conda
    $ conda install shapely --channel conda-forge

See the `installation documentation <https://shapely.readthedocs.io/en/latest/installation.html>`__
for more details and advanced installation instructions.

Integration
===========

Shapely does not read or write data files, but it can serialize and deserialize
using several well known formats and protocols. The shapely.wkb and shapely.wkt
modules provide dumpers and loaders inspired by Python's pickle module.

.. code-block:: pycon

    >>> from shapely.wkt import dumps, loads
    >>> dumps(loads('POINT (0 0)'))
    'POINT (0.0000000000000000 0.0000000000000000)'

Shapely can also integrate with other Python GIS packages using GeoJSON-like
dicts.

.. code-block:: pycon

    >>> import json
    >>> from shapely.geometry import mapping, shape
    >>> s = shape(json.loads('{"type": "Point", "coordinates": [0.0, 0.0]}'))
    >>> s
    <POINT (0 0)>
    >>> print(json.dumps(mapping(s)))
    {"type": "Point", "coordinates": [0.0, 0.0]}

Support
=======

Questions about using Shapely may be asked on the `GIS StackExchange
<https://gis.stackexchange.com/questions/tagged/shapely>`__ using the "shapely"
tag.

Bugs may be reported at https://github.com/shapely/shapely/issues.

Copyright & License
===================

Shapely is licensed under BSD 3-Clause license.
GEOS is available under the terms of GNU Lesser General Public License (LGPL) 2.1 at https://libgeos.org.
